import React, { useMemo, useCallback } from 'react';
import Select, {SingleValue, components} from 'react-select';
import { FixedSizeList as List } from 'react-window';
import '../custom_css/ReactSelect.css';

interface Option {
    value: string;
    label: string;
}

interface StyledSelectProps {
    options: Option[];
    placeholder: string;
    onChange: (selectedOption: SingleValue<Option>) => void;
    value: SingleValue<Option> | null;
    disabled?: boolean;
    isSearchable?: boolean;
    maxMenuHeight?: number;
    menuIsOpen?: boolean;
    onMenuOpen?: () => void;
    onMenuClose?: () => void;
    filterOption?: (option: any, inputValue: string) => boolean;
    noOptionsMessage?: (obj: { inputValue: string }) => string;
    loadingMessage?: () => string;
    isLoading?: boolean;
}

// Virtualized Menu List component for better performance with large datasets
const MenuList = (props: any) => {
    const { options, children, maxHeight, getValue } = props;
    const [value] = getValue();
    const initialOffset = options.indexOf(value) * 35;

    if (!children || !Array.isArray(children) || children.length === 0) {
        return <div style={{ textAlign: 'center', padding: '10px' }}>No options</div>;
    }

    const height = Math.min(maxHeight, children.length * 35);

    return (
        <List
            height={height}
            itemCount={children.length}
            itemSize={35}
            initialScrollOffset={initialOffset}
            itemData={children}
        >
            {({ index, style, data }) => (
                <div style={style}>
                    {data[index]}
                </div>
            )}
        </List>
    );
};

const ReactSelect: React.FC<StyledSelectProps> = ({
    options,
    placeholder,
    onChange,
    value,
    disabled = false,
    isSearchable = true,
    maxMenuHeight = 200,
    menuIsOpen,
    onMenuOpen,
    onMenuClose,
    filterOption,
    noOptionsMessage,
    loadingMessage,
    isLoading = false
}) => {
    // Memoize custom styles to prevent recreation on every render
    const customStyles = useMemo(() => ({
        control: (provided: any, state: any) => ({
            ...provided,
            minHeight: '40px',
            border: state.isFocused ? '1px solid #0d6efd' : '1px solid var(--bs-gray-300)',
            boxShadow: state.isFocused ? '0 0 0 0.2rem rgba(13, 110, 253, 0.25)' : 'none',
            '&:hover': {
                border: '1px solid var(--bs-gray-400)',
            }
        }),
        menu: (provided: any) => ({
            ...provided,
            zIndex: 9999,
            marginTop: '2px',
        }),
        menuList: (provided: any) => ({
            ...provided,
            padding: 0,
        }),
        option: (provided: any, state: any) => ({
            ...provided,
            backgroundColor: state.isSelected
                ? '#0d6efd'
                : state.isFocused
                    ? '#e9ecef'
                    : 'white',
            color: state.isSelected ? 'white' : '#495057',
            padding: '8px 12px',
            cursor: 'pointer',
            fontSize: '14px',
            '&:active': {
                backgroundColor: '#0d6efd',
            },
        }),
        valueContainer: (provided: any) => ({
            ...provided,
            padding: '2px 8px',
        }),
        input: (provided: any) => ({
            ...provided,
            margin: '0',
            padding: '0',
        }),
        placeholder: (provided: any) => ({
            ...provided,
            color: '#6c757d',
        }),
        loadingIndicator: (provided: any) => ({
            ...provided,
            color: '#0d6efd',
        }),
    }), []);

    // Memoize components to prevent recreation
    const selectComponents = useMemo(() => ({
        MenuList: options.length > 50 ? MenuList : components.MenuList,
        IndicatorSeparator: () => null,
    }), [options.length]);

    // Default filter function with performance optimization
    const defaultFilterOption = useCallback((option: any, inputValue: string) => {
        if (!inputValue) return true;
        const searchValue = inputValue.toLowerCase();
        return option.label.toLowerCase().includes(searchValue) ||
               option.value.toLowerCase().includes(searchValue);
    }, []);

    return (
        <Select<Option>
            className="react-select-styled custom-select"
            classNamePrefix="react-select"
            options={options}
            placeholder={placeholder}
            onChange={onChange}
            menuPlacement="auto"
            value={value}
            isDisabled={disabled}
            isSearchable={isSearchable}
            maxMenuHeight={maxMenuHeight}
            menuIsOpen={menuIsOpen}
            onMenuOpen={onMenuOpen}
            onMenuClose={onMenuClose}
            styles={customStyles}
            components={selectComponents}
            filterOption={filterOption || defaultFilterOption}
            noOptionsMessage={noOptionsMessage || (() => 'No options found')}
            loadingMessage={loadingMessage || (() => 'Loading...')}
            isLoading={isLoading}
            // Performance optimizations
            blurInputOnSelect={true}
            captureMenuScroll={false}
            closeMenuOnSelect={true}
            escapeClearsValue={true}
            hideSelectedOptions={false}
            isClearable={false}
            isMulti={false}
            menuShouldBlockScroll={false}
            menuShouldScrollIntoView={false}
            openMenuOnClick={true}
            openMenuOnFocus={false}
            pageSize={5}
            tabSelectsValue={true}
        />
    );
};

export default React.memo(ReactSelect);